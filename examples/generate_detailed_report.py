#!/usr/bin/env python3
"""
FT语料评估详细HTML报告生成示例

此脚本演示如何使用新的HTML报告生成器创建详细的评估报告。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.core.batch_processor import BatchProcessor
from ft_corpus_evaluator.core.evaluator_manager import EvaluatorManager
from ft_corpus_evaluator.reports.html_report_generator import HTMLReportGenerator


def main():
    # 设置路径
    corpus_dir = project_root / "corpus"
    output_dir = project_root / "output" / "detailed_reports"
    config_path = project_root / "ft_corpus_evaluator" / "config" / "config.json"
    
    print("🚀 开始生成详细HTML评估报告...")
    print(f"📁 语料目录: {corpus_dir}")
    print(f"📄 输出目录: {output_dir}")
    
    # 检查语料目录是否存在
    if not corpus_dir.exists():
        print(f"❌ 语料目录不存在: {corpus_dir}")
        return

    # 检查语料文件
    corpus_files = list(corpus_dir.glob("*.md"))
    print(f"📁 找到 {len(corpus_files)} 个语料文件:")
    for file in corpus_files[:5]:  # 显示前5个文件
        print(f"   📄 {file.name}")
    if len(corpus_files) > 5:
        print(f"   ... 还有 {len(corpus_files) - 5} 个文件")

    if not corpus_files:
        print("❌ 未找到任何.md文件")
        return
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 初始化评估管理器
        evaluator_manager = EvaluatorManager(str(config_path) if config_path.exists() else None)
        
        # 初始化批处理器
        batch_processor = BatchProcessor(evaluator_manager, max_workers=4)
        
        # 处理语料目录
        print("📊 开始评估语料...")
        result = batch_processor.process_directory(
            corpus_dir=corpus_dir,
            file_pattern="*.md",
            output_dir=output_dir
        )
        
        if "error" in result:
            print(f"❌ 处理失败: {result['error']}")
            return
        
        # 生成额外的详细报告
        html_generator = HTMLReportGenerator()
        
        # 生成主报告
        main_report_path = output_dir / "comprehensive_evaluation_report.html"
        html_generator.generate_detailed_report(
            result, 
            main_report_path,
            "FT语料综合评估报告"
        )
        
        # 打印结果摘要
        summary = result.get('summary', {})
        print("\n📈 评估完成！结果摘要:")
        print(f"   总语料数: {summary.get('total_corpus_count', 0)}")
        print(f"   失败数量: {summary.get('total_failed_count', 0)}")
        print(f"   评估维度: {len(summary.get('dimensions_evaluated', []))}")
        
        # 显示生成的报告文件
        print("\n📋 生成的报告文件:")
        for report_file in output_dir.glob("*.html"):
            print(f"   📄 {report_file.name}")
        
        print(f"\n✅ 详细HTML报告已生成到: {output_dir}")
        print(f"🌐 主报告文件: {main_report_path}")
        
        # 如果是在支持的环境中，可以尝试打开报告
        try:
            import webbrowser
            print(f"\n🔗 尝试在浏览器中打开报告...")
            webbrowser.open(f"file://{main_report_path.absolute()}")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {e}")
            print(f"请手动打开: file://{main_report_path.absolute()}")
        
    except Exception as e:
        print(f"❌ 生成报告时发生错误: {e}")
        import traceback
        traceback.print_exc()


def generate_sample_data_report():
    """生成示例数据的报告，用于演示"""
    from datetime import datetime
    
    # 创建示例评估结果数据
    sample_result = {
        'corpus_results': [
            {
                'corpus_id': 'RAN-6612580',
                'file_path': 'corpus/测试用例 RAN-6612580.md',
                'results': {
                    'completeness': {
                        'required_fields': {
                            'score': 85.0,
                            'level': 'warning',
                            'message': '部分必填字段缺失',
                            'details': {
                                'missing_fields': ['test_info.tc_expected_results'],
                                'present_fields': 8,
                                'total_fields': 9
                            },
                            'suggestions': ['添加测试预期结果字段']
                        },
                        'content_length': {
                            'score': 92.0,
                            'level': 'info',
                            'message': '内容长度适中',
                            'details': {'total_length': 2500},
                            'suggestions': []
                        }
                    },
                    'correctness': {
                        'format_correctness': {
                            'score': 78.0,
                            'level': 'warning',
                            'message': '格式存在问题',
                            'details': {'format_issues': ['date_format']},
                            'suggestions': ['修正日期格式为YYYY-MM-DD']
                        }
                    }
                }
            }
        ],
        'collection_results': {
            'completeness': {
                'field_coverage': {
                    'score': 88.5,
                    'level': 'info',
                    'message': '字段覆盖率良好',
                    'details': {'coverage_rate': 0.885},
                    'suggestions': ['提高必填字段完整性']
                }
            },
            'correctness': {
                'format_consistency': {
                    'score': 75.0,
                    'level': 'warning',
                    'message': '格式一致性需要改进',
                    'details': {'consistency_rate': 0.75},
                    'suggestions': ['统一日期格式', '规范链接格式']
                }
            }
        },
        'summary': {
            'total_corpus_count': 1,
            'total_failed_count': 0,
            'dimensions_evaluated': ['completeness', 'correctness'],
            'failed_files': []
        }
    }
    
    # 生成示例报告
    output_dir = Path("output/sample_reports")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    html_generator = HTMLReportGenerator()
    report_path = output_dir / "sample_detailed_report.html"
    
    html_generator.generate_detailed_report(
        sample_result,
        report_path,
        "FT语料评估示例报告"
    )
    
    print(f"✅ 示例报告已生成: {report_path}")
    return report_path


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--sample":
        # 生成示例报告
        sample_path = generate_sample_data_report()
        try:
            import webbrowser
            webbrowser.open(f"file://{sample_path.absolute()}")
        except:
            print(f"请手动打开: file://{sample_path.absolute()}")
    else:
        # 生成实际语料的报告
        main()
