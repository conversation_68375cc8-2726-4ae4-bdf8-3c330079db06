#!/usr/bin/env python3
"""
测试HTML报告中语料详细信息的显示效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.cli.main import run_evaluation
import argparse


def test_html_details():
    """测试HTML报告的语料详细信息显示"""
    
    print("🧪 测试HTML报告语料详细信息显示")
    
    corpus_dir = Path("corpus")
    output_dir = Path("output/test_details")
    
    if not corpus_dir.exists():
        print("❌ 语料目录不存在")
        return False
    
    # 创建模拟的args对象
    class MockArgs:
        def __init__(self):
            self.config = None
            self.dimensions = ["all"]
    
    mock_args = MockArgs()
    
    try:
        print("📊 运行评估...")
        result = run_evaluation(mock_args, corpus_dir, output_dir)
        
        if result != 0:
            print("❌ 评估失败")
            return False
        
        # 检查HTML报告
        html_report = output_dir / "detailed_evaluation_report.html"
        if not html_report.exists():
            print("❌ HTML报告未生成")
            return False
        
        # 读取HTML内容并检查关键元素
        html_content = html_report.read_text(encoding='utf-8')
        
        # 检查关键内容
        checks = [
            ("语料详细信息", "📋 语料详细信息" in html_content),
            ("语料ID显示", "RAN-" in html_content),
            ("评分显示", "metric-score" in html_content),
            ("详细信息展开", "expandable" in html_content),
            ("维度分组", "完整性" in html_content and "正确性" in html_content),
            ("改进建议", "建议:" in html_content),
            ("详细数据", "present_fields" in html_content),
        ]
        
        print("\n🔍 检查结果:")
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 所有检查通过！HTML报告已生成: {html_report}")
            
            # 显示一些统计信息
            corpus_count = html_content.count("expandable")
            metric_count = html_content.count("metric-score")
            suggestion_count = html_content.count("建议:")
            
            print(f"📊 统计信息:")
            print(f"  - 语料数量: {corpus_count}")
            print(f"  - 指标数量: {metric_count}")
            print(f"  - 建议数量: {suggestion_count}")
            
            return True
        else:
            print("\n❌ 部分检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_html_details()
    if success:
        print("\n✅ 测试成功！语料详细信息显示正常。")
        
        # 尝试打开报告
        try:
            import webbrowser
            report_path = Path("output/test_details/detailed_evaluation_report.html")
            if report_path.exists():
                print(f"🔗 在浏览器中打开报告...")
                webbrowser.open(f"file://{report_path.absolute()}")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {e}")
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
