{"total_corpus": 4, "summary": {"total_corpus_count": 4, "total_failed_count": 0, "dimensions_evaluated": ["completeness", "correctness", "difficulty"], "failed_files": []}, "first_few_results": [{"file_path": "corpus/RAN-5869391.md", "corpus_id": "RAN-5869391", "results": {"completeness": {"required_fields": {"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, "content_structure": {"metric_name": "content_structure", "dimension": "completeness", "score": 75, "weight": 1.0, "level": "warning", "message": "Structure issues found: 1", "details": {"structure_issues": ["Test steps are missing or too short"], "issues_count": 1}, "suggestions": ["Fix: Test steps are missing or too short"]}, "test_steps_completeness": {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 40, "weight": 1.0, "level": "critical", "message": "Test steps are incomplete", "details": {"steps_length": 5, "expected_results_length": 5, "content_ratio": 1.0, "issues": ["Test steps content too short: 5 chars (minimum: 20)", "Expected results content too short: 5 chars (minimum: 20)"]}, "suggestions": ["Expand test steps content (current: 5 chars, minimum: 20)", "Expand expected results content (current: 5 chars, minimum: 20)"]}, "code_snippets_completeness": {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 70, "weight": 0.8, "level": "warning", "message": "Code snippets need improvement", "details": {"snippets_count": 34, "incomplete_snippets": [{"index": 18, "issues": ["content too short"]}, {"index": 23, "issues": ["content too short"]}, {"index": 32, "issues": ["missing or unknown language"]}, {"index": 33, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 4"]}, "suggestions": ["Fix snippet 18: content too short", "Fix snippet 23: content too short", "Fix snippet 32: missing or unknown language"]}}, "correctness": {"format_correctness": {"metric_name": "format_correctness", "dimension": "correctness", "score": 75, "weight": 1.0, "level": "warning", "message": "Format issues found: 1", "details": {"format_issues": ["Invalid Gerrit link format"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid Gerrit link format"]}, "logical_consistency": {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 5, "results_length": 5}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, "data_validity": {"metric_name": "data_validity", "dimension": "correctness", "score": 80, "weight": 1.0, "level": "info", "message": "Data is valid", "details": {"validity_issues": ["TC steps content is too short", "Code snippet 33 has unknown language", "Code snippet 34 has unknown language"], "duplicate_steps": [], "issues_count": 3}, "suggestions": ["Fix: TC steps content is too short", "Fix: Code snippet 33 has unknown language", "Fix: Code snippet 34 has unknown language"]}}, "difficulty": {"technical_complexity": {"metric_name": "technical_complexity", "dimension": "difficulty", "score": 38, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Medium complexity keywords: 1", "Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 1, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, "business_complexity": {"metric_name": "business_complexity", "dimension": "difficulty", "score": 37, "weight": 1.0, "level": "info", "message": "Medium business complexity", "details": {"difficulty_level": "Medium", "business_factors": ["Business domains: ['telecom']"], "domain_matches": {"telecom": 3}, "scenario_complexity": 0, "integration_complexity": 13}, "suggestions": []}, "test_coverage_complexity": {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 37, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: negative", "Edge cases: 3"], "test_types": ["negative"], "test_scenarios": 0, "edge_cases": ["失败", "超时", "满"], "data_variations": 3}, "suggestions": []}}}}, {"file_path": "corpus/RAN-5946243 RRC建立优化场景MSG5的传递路径：LUCM-_LUC（UlDcchTranmissionMessage）传递远近点标识.md", "corpus_id": "RAN-5946243", "results": {"completeness": {"required_fields": {"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, "content_structure": {"metric_name": "content_structure", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Content structure is complete", "details": {"structure_issues": [], "issues_count": 0}, "suggestions": []}, "test_steps_completeness": {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Test steps are complete", "details": {"steps_length": 69, "expected_results_length": 53, "content_ratio": 1.3018867924528301, "issues": []}, "suggestions": []}, "code_snippets_completeness": {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 70, "weight": 0.8, "level": "warning", "message": "Code snippets need improvement", "details": {"snippets_count": 20, "incomplete_snippets": [{"index": 15, "issues": ["missing or unknown language"]}, {"index": 16, "issues": ["missing or unknown language"]}, {"index": 17, "issues": ["missing or unknown language"]}, {"index": 18, "issues": ["missing or unknown language"]}, {"index": 19, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 5"]}, "suggestions": ["Fix snippet 15: missing or unknown language", "Fix snippet 16: missing or unknown language", "Fix snippet 17: missing or unknown language"]}}, "correctness": {"format_correctness": {"metric_name": "format_correctness", "dimension": "correctness", "score": 80, "weight": 1.0, "level": "info", "message": "Format validation passed", "details": {"format_issues": ["Invalid date format (expected: YYYY-MM-DD)"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid date format (expected: YYYY-MM-DD)"]}, "logical_consistency": {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 69, "results_length": 53}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, "data_validity": {"metric_name": "data_validity", "dimension": "correctness", "score": 0, "weight": 1.0, "level": "critical", "message": "Critical data validity issues", "details": {"validity_issues": ["Date format is invalid", "Code snippet 16 has unknown language", "Code snippet 17 has unknown language", "Code snippet 18 has unknown language", "Code snippet 19 has unknown language", "Code snippet 20 has unknown language", "Duplicate test steps found: 25"], "duplicate_steps": [8, 25, 28, 29, 32, 33, 34, 36, 37, 39, 40, 43, 46, 48, 49, 50, 52, 53, 55, 56, 57, 58, 59, 61, 62], "issues_count": 7}, "suggestions": ["Fix: Date format is invalid", "Fix: Code snippet 16 has unknown language", "Fix: Code snippet 17 has unknown language", "Fix: Code snippet 18 has unknown language", "Fix: Code snippet 19 has unknown language"]}}, "difficulty": {"technical_complexity": {"metric_name": "technical_complexity", "dimension": "difficulty", "score": 30, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 0, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, "business_complexity": {"metric_name": "business_complexity", "dimension": "difficulty", "score": 18, "weight": 1.0, "level": "info", "message": "Low business complexity", "details": {"difficulty_level": "Low", "business_factors": [], "domain_matches": {}, "scenario_complexity": 5, "integration_complexity": 13}, "suggestions": []}, "test_coverage_complexity": {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 18, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: negative", "Edge cases: 1"], "test_types": ["negative"], "test_scenarios": 0, "edge_cases": ["错误"], "data_variations": 0}, "suggestions": []}}}}, {"file_path": "corpus/测试用例 RAN-6808752_ UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验.md", "corpus_id": "RAN-6808752", "results": {"completeness": {"required_fields": {"metric_name": "required_fields", "dimension": "completeness", "score": 100.0, "weight": 1.0, "level": "info", "message": "All required fields are present", "details": {"missing_fields": [], "present_fields": 9, "total_fields": 9, "completion_rate": "9/9"}, "suggestions": []}, "content_structure": {"metric_name": "content_structure", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Content structure is complete", "details": {"structure_issues": [], "issues_count": 0}, "suggestions": []}, "test_steps_completeness": {"metric_name": "test_steps_completeness", "dimension": "completeness", "score": 100, "weight": 1.0, "level": "info", "message": "Test steps are complete", "details": {"steps_length": 56, "expected_results_length": 101, "content_ratio": 0.5544554455445545, "issues": []}, "suggestions": []}, "code_snippets_completeness": {"metric_name": "code_snippets_completeness", "dimension": "completeness", "score": 80, "weight": 0.8, "level": "info", "message": "Code snippets are complete", "details": {"snippets_count": 15, "incomplete_snippets": [{"index": 13, "issues": ["missing or unknown language"]}, {"index": 14, "issues": ["missing or unknown language"]}], "issues": ["Incomplete code snippets: 2"]}, "suggestions": ["Fix snippet 13: missing or unknown language", "Fix snippet 14: missing or unknown language"]}}, "correctness": {"format_correctness": {"metric_name": "format_correctness", "dimension": "correctness", "score": 80, "weight": 1.0, "level": "info", "message": "Format validation passed", "details": {"format_issues": ["Invalid date format (expected: YYYY-MM-DD)"], "invalid_tags": [], "issues_count": 1}, "suggestions": ["Fix format issue: Invalid date format (expected: YYYY-MM-DD)"]}, "logical_consistency": {"metric_name": "logical_consistency", "dimension": "correctness", "score": 85, "weight": 1.0, "level": "info", "message": "Content is logically consistent", "details": {"consistency_issues": ["Business and code tags are inconsistent"], "issues_count": 1, "steps_length": 56, "results_length": 101}, "suggestions": ["Fix: Business and code tags are inconsistent"]}, "data_validity": {"metric_name": "data_validity", "dimension": "correctness", "score": 0, "weight": 1.0, "level": "critical", "message": "Critical data validity issues", "details": {"validity_issues": ["Date format is invalid", "Code snippet 14 has unknown language", "Code snippet 15 has unknown language", "Duplicate test steps found: 26"], "duplicate_steps": [12, 17, 20, 21, 24, 25, 27, 28, 30, 35, 36, 37, 39, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54], "issues_count": 4}, "suggestions": ["Fix: Date format is invalid", "Fix: Code snippet 14 has unknown language", "Fix: Code snippet 15 has unknown language", "Fix: Duplicate test steps found: 26"]}}, "difficulty": {"technical_complexity": {"metric_name": "technical_complexity", "dimension": "difficulty", "score": 30, "weight": 1.0, "level": "info", "message": "Low technical complexity", "details": {"difficulty_level": "Low", "complexity_factors": ["Complex code patterns detected"], "keyword_counts": {"high": 0, "medium": 0, "low": 0}, "code_complexity": 30, "steps_complexity": 0}, "suggestions": []}, "business_complexity": {"metric_name": "business_complexity", "dimension": "difficulty", "score": 13, "weight": 1.0, "level": "info", "message": "Low business complexity", "details": {"difficulty_level": "Low", "business_factors": [], "domain_matches": {}, "scenario_complexity": 0, "integration_complexity": 13}, "suggestions": []}, "test_coverage_complexity": {"metric_name": "test_coverage_complexity", "dimension": "difficulty", "score": 18, "weight": 0.8, "level": "warning", "message": "Limited test coverage", "details": {"difficulty_level": "Low", "coverage_factors": ["Test types: negative", "Edge cases: 1"], "test_types": ["negative"], "test_scenarios": 0, "edge_cases": ["失败"], "data_variations": 0}, "suggestions": []}}}}]}