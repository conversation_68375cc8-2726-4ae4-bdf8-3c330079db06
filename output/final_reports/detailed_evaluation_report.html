<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT语料质量评估详细报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .summary-card:hover {
            transform: translateY(-5px);
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .summary-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-card .description {
            color: #888;
            font-size: 0.9em;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .dimension-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .dimension-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dimension-title {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .dimension-score {
            font-size: 1.2em;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
        }
        
        .metric-list {
            padding: 20px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-name {
            font-weight: 500;
        }
        
        .metric-score {
            font-weight: bold;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        
        .score-excellent { background-color: #28a745; }
        .score-good { background-color: #17a2b8; }
        .score-fair { background-color: #ffc107; color: #333; }
        .score-poor { background-color: #dc3545; }
        
        .level-critical { background-color: #dc3545; }
        .level-warning { background-color: #ffc107; color: #333; }
        .level-info { background-color: #17a2b8; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .expandable {
            cursor: pointer;
            user-select: none;
        }
        
        .expandable:hover {
            background-color: #f8f9fa;
        }
        
        .details {
            display: none;
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .details.show {
            display: block;
        }
        
        .tag {
            display: inline-block;
            padding: 3px 8px;
            background-color: #e9ecef;
            color: #495057;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }
        
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .recommendations h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .recommendations li {
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
        }
        
        .recommendations li:last-child {
            border-bottom: none;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .dimension-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        </style>
    <script>
        function toggleDetails(element) {
            const details = element.nextElementSibling;
            details.classList.toggle('show');
            
            const icon = element.querySelector('.toggle-icon');
            if (icon) {
                icon.textContent = details.classList.contains('show') ? '▼' : '▶';
            }
        }
        
        function getScoreClass(score) {
            if (score >= 90) return 'score-excellent';
            if (score >= 80) return 'score-good';
            if (score >= 60) return 'score-fair';
            return 'score-poor';
        }
        
        function getLevelClass(level) {
            switch(level) {
                case 'critical': return 'level-critical';
                case 'warning': return 'level-warning';
                case 'info': return 'level-info';
                default: return 'level-info';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化进度条动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });
        });
        </script>
</head>
<body>
    <div class="container">
        
        <div class="header">
            <h1>FT语料质量评估详细报告</h1>
            <div class="subtitle">
                生成时间: 2025-07-25 15:57:59 |
                总语料数: 4 |
                评估维度: 3
            </div>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>总体评分</h3>
                <div class="value score-fair">63.1</div>
                <div class="description">平均分数</div>
            </div>
            <div class="summary-card">
                <h3>语料总数</h3>
                <div class="value">4</div>
                <div class="description">已评估语料</div>
            </div>
            <div class="summary-card">
                <h3>失败数量</h3>
                <div class="value score-poor">0</div>
                <div class="description">处理失败</div>
            </div>
            <div class="summary-card">
                <h3>优秀率</h3>
                <div class="value score-excellent">0.0%</div>
                <div class="description">90分以上</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>📊 维度评估概览</h2>
            </div>
            <div class="section-content">
                
            <div class="dimension-card">
                <div class="dimension-header">
                    <div class="dimension-title">完整性</div>
                    <div class="dimension-score score-excellent">100.0</div>
                </div>
                <div class="metric-list">
                    
                <div class="metric-item">
                    <div class="metric-name">field_missing_distribution</div>
                    <div class="metric-score score-excellent">100.0</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">completeness_pattern_analysis</div>
                    <div class="metric-score score-excellent">100.0</div>
                </div>
                </div>
            </div>
            <div class="dimension-card">
                <div class="dimension-header">
                    <div class="dimension-title">正确性</div>
                    <div class="dimension-score score-poor">50.0</div>
                </div>
                <div class="metric-list">
                    
                <div class="metric-item">
                    <div class="metric-name">format_issues_distribution</div>
                    <div class="metric-score score-poor">0.0</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">consistency_pattern_analysis</div>
                    <div class="metric-score score-excellent">100.0</div>
                </div>
                </div>
            </div>
            <div class="dimension-card">
                <div class="dimension-header">
                    <div class="dimension-title">难度</div>
                    <div class="dimension-score score-poor">39.2</div>
                </div>
                <div class="metric-list">
                    
                <div class="metric-item">
                    <div class="metric-name">technical_keyword_frequency</div>
                    <div class="metric-score score-poor">25.0</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">difficulty_distribution</div>
                    <div class="metric-score score-poor">53.3</div>
                </div>
                </div>
            </div>
            </div>
        </div>
        
            <div class="section">
                <div class="section-header">
                    <h2>📈 完整性 - 详细分析</h2>
                </div>
                <div class="section-content">
                    
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>field_missing_distribution</h4>
                
            <div class="progress-bar">
                <div class="progress-fill score-excellent" style="width: 100.0%"></div>
            </div>
                <div style="margin: 10px 0;">
                    <span class="metric-score level-info">INFO</span>
                    <span style="margin-left: 10px;">字段完整性良好</span>
                </div>
                <div style="margin: 10px 0; font-size: 0.9em; color: #666;"><div><strong>total_corpus:</strong> 4</div><div><strong>most_missing_fields:</strong> 0 项</div><div><strong>field_missing_distribution:</strong> {}</div><div><strong>average_completion_rate:</strong> 1.0</div></div>
                
            </div>
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>completeness_pattern_analysis</h4>
                
            <div class="progress-bar">
                <div class="progress-fill score-excellent" style="width: 100.0%"></div>
            </div>
                <div style="margin: 10px 0;">
                    <span class="metric-score level-info">INFO</span>
                    <span style="margin-left: 10px;">完整性模式优秀，平均完整率 100.0%</span>
                </div>
                <div style="margin: 10px 0; font-size: 0.9em; color: #666;"><div><strong>total_corpus:</strong> 4</div><div><strong>average_completion_rate:</strong> 1.0</div><div><strong>completion_distribution:</strong> {'excellent': 4, 'good': 0, 'fair': 0, 'poor': 0}</div></div>
                
            </div>
                </div>
            </div>
            <div class="section">
                <div class="section-header">
                    <h2>📈 正确性 - 详细分析</h2>
                </div>
                <div class="section-content">
                    
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>format_issues_distribution</h4>
                
            <div class="progress-bar">
                <div class="progress-fill score-poor" style="width: 0.0%"></div>
            </div>
                <div style="margin: 10px 0;">
                    <span class="metric-score level-critical">CRITICAL</span>
                    <span style="margin-left: 10px;">格式质量较差，正确率 0.0%</span>
                </div>
                <div style="margin: 10px 0; font-size: 0.9em; color: #666;"><div><strong>total_corpus:</strong> 4</div><div><strong>corpus_with_issues:</strong> 4</div><div><strong>format_correctness_rate:</strong> 0.0</div><div><strong>most_common_issues:</strong> 2 项</div><div><strong>issue_types_distribution:</strong> {'gerrit_link_format': 2, 'date_format': 2}</div></div>
                
            </div>
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>consistency_pattern_analysis</h4>
                
            <div class="progress-bar">
                <div class="progress-fill score-excellent" style="width: 100.0%"></div>
            </div>
                <div style="margin: 10px 0;">
                    <span class="metric-score level-info">INFO</span>
                    <span style="margin-left: 10px;">逻辑一致性优秀，一致率 100.0%</span>
                </div>
                <div style="margin: 10px 0; font-size: 0.9em; color: #666;"><div><strong>total_corpus:</strong> 4</div><div><strong>corpus_with_issues:</strong> 0</div><div><strong>consistency_rate:</strong> 100.0</div><div><strong>most_common_issues:</strong> 0 项</div><div><strong>avg_steps_length:</strong> 33.25</div><div><strong>avg_results_length:</strong> 40.5</div><div><strong>issue_types_distribution:</strong> {}</div></div>
                
            </div>
                </div>
            </div>
            <div class="section">
                <div class="section-header">
                    <h2>📈 难度 - 详细分析</h2>
                </div>
                <div class="section-content">
                    
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>technical_keyword_frequency</h4>
                
            <div class="progress-bar">
                <div class="progress-fill score-poor" style="width: 25.0%"></div>
            </div>
                <div style="margin: 10px 0;">
                    <span class="metric-score level-critical">CRITICAL</span>
                    <span style="margin-left: 10px;">技术关键词多样性较低，共 1 个不同关键词</span>
                </div>
                <div style="margin: 10px 0; font-size: 0.9em; color: #666;"><div><strong>total_unique_keywords:</strong> 1</div><div><strong>total_corpus:</strong> 4</div><div><strong>most_common_keywords:</strong> 1 项</div><div><strong>keyword_frequency_distribution:</strong> {'配置': 1}</div></div>
                
            </div>
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>difficulty_distribution</h4>
                
            <div class="progress-bar">
                <div class="progress-fill score-poor" style="width: 53.333333333333336%"></div>
            </div>
                <div style="margin: 10px 0;">
                    <span class="metric-score level-critical">CRITICAL</span>
                    <span style="margin-left: 10px;">难度分布不够合理，平均复杂度 1.2</span>
                </div>
                <div style="margin: 10px 0; font-size: 0.9em; color: #666;"><div><strong>total_corpus:</strong> 4</div><div><strong>level_distribution:</strong> {'high': 0, 'medium': 0, 'low': 4}</div><div><strong>level_ratios:</strong> {'high': 0.0, 'medium': 0.0, 'low': 1.0}</div><div><strong>average_complexity:</strong> 1.25</div><div><strong>ideal_distribution:</strong> {'high': 0.2, 'medium': 0.5, 'low': 0.3}</div></div>
                
            </div>
                </div>
            </div>
        
        <div class="section">
            <div class="section-header">
                <h2>📋 语料详细信息</h2>
            </div>
            <div class="section-content">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>语料ID</th>
                                <th>文件名</th>
                                <th>总分</th>
                                <th>指标数</th>
                            </tr>
                        </thead>
                        <tbody>
                            
            <tr class="expandable" onclick="toggleDetails(this)">
                <td><span class="toggle-icon">▶</span> RAN-5869391</td>
                <td>RAN-5869391.md</td>
                <td><span class="metric-score score-fair">63.7</span></td>
                <td>10</td>
            </tr>
            <tr>
                <td colspan="4" class="details">
                    <div style='padding: 15px;'><h5>完整性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>必填字段</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>All required fields are present</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>present_fields:</strong> 9</div><div><strong>total_fields:</strong> 9</div><div><strong>completion_rate:</strong> 9/9</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>content_structure</strong>
                            <span class='metric-score score-fair'>75.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Structure issues found: 1</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>structure_issues:</strong> Test steps are missing or too short</div><div><strong>issues_count:</strong> 1</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Test steps are missing or too short</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_steps_completeness</strong>
                            <span class='metric-score score-poor'>40.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Test steps are incomplete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>steps_length:</strong> 5</div><div><strong>expected_results_length:</strong> 5</div><div><strong>content_ratio:</strong> 1.0</div><div><strong>issues:</strong> Test steps content too short: 5 chars (minimum: 20), Expected results content too short: 5 chars (minimum: 20)</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Expand test steps content (current: 5 chars, minimum: 20)</li><li>Expand expected results content (current: 5 chars, minimum: 20)</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>code_snippets_completeness</strong>
                            <span class='metric-score score-fair'>70.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Code snippets need improvement</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>snippets_count:</strong> 34</div><div><strong>incomplete_snippets:</strong> {'index': 18, 'issues': ['content too short']}, {'index': 23, 'issues': ['content too short']}, {'index': 32, 'issues': ['missing or unknown language']} ... (共4项)</div><div><strong>issues:</strong> Incomplete code snippets: 4</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix snippet 18: content too short</li><li>Fix snippet 23: content too short</li><li>Fix snippet 32: missing or unknown language</li></ul></div></div></div><h5>正确性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>格式正确性</strong>
                            <span class='metric-score score-fair'>75.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Format issues found: 1</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>format_issues:</strong> Invalid Gerrit link format</div><div><strong>issues_count:</strong> 1</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix format issue: Invalid Gerrit link format</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>logical_consistency</strong>
                            <span class='metric-score score-good'>85.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Content is logically consistent</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>consistency_issues:</strong> Business and code tags are inconsistent</div><div><strong>issues_count:</strong> 1</div><div><strong>steps_length:</strong> 5</div><div><strong>results_length:</strong> 5</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Business and code tags are inconsistent</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>data_validity</strong>
                            <span class='metric-score score-good'>80.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Data is valid</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>validity_issues:</strong> TC steps content is too short, Code snippet 33 has unknown language, Code snippet 34 has unknown language</div><div><strong>issues_count:</strong> 3</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: TC steps content is too short</li><li>Fix: Code snippet 33 has unknown language</li><li>Fix: Code snippet 34 has unknown language</li></ul></div></div></div><h5>难度</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>technical_complexity</strong>
                            <span class='metric-score score-poor'>38.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Low technical complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>complexity_factors:</strong> Medium complexity keywords: 1, Complex code patterns detected</div><div><strong>code_complexity:</strong> 30</div><div><strong>steps_complexity:</strong> 0</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>business_complexity</strong>
                            <span class='metric-score score-poor'>37.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Medium business complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Medium</div><div><strong>business_factors:</strong> Business domains: ['telecom']</div><div><strong>scenario_complexity:</strong> 0</div><div><strong>integration_complexity:</strong> 13</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_coverage_complexity</strong>
                            <span class='metric-score score-poor'>37.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Limited test coverage</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>coverage_factors:</strong> Test types: negative, Edge cases: 3</div><div><strong>test_types:</strong> negative</div><div><strong>test_scenarios:</strong> 0</div><div><strong>edge_cases:</strong> 失败, 超时, 满</div><div><strong>data_variations:</strong> 3</div></div></div></div></div>
                </td>
            </tr>
            <tr class="expandable" onclick="toggleDetails(this)">
                <td><span class="toggle-icon">▶</span> RAN-5946243</td>
                <td>RAN-5946243 RRC建立优化场景MSG5的传递路径：LUCM-_LUC（UlDcchTranmissionMessage）传递远近点标识.md</td>
                <td><span class="metric-score score-fair">60.1</span></td>
                <td>10</td>
            </tr>
            <tr>
                <td colspan="4" class="details">
                    <div style='padding: 15px;'><h5>完整性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>必填字段</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>All required fields are present</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>present_fields:</strong> 9</div><div><strong>total_fields:</strong> 9</div><div><strong>completion_rate:</strong> 9/9</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>content_structure</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Content structure is complete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>issues_count:</strong> 0</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_steps_completeness</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Test steps are complete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>steps_length:</strong> 69</div><div><strong>expected_results_length:</strong> 53</div><div><strong>content_ratio:</strong> 1.3018867924528301</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>code_snippets_completeness</strong>
                            <span class='metric-score score-fair'>70.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Code snippets need improvement</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>snippets_count:</strong> 20</div><div><strong>incomplete_snippets:</strong> {'index': 15, 'issues': ['missing or unknown language']}, {'index': 16, 'issues': ['missing or unknown language']}, {'index': 17, 'issues': ['missing or unknown language']} ... (共5项)</div><div><strong>issues:</strong> Incomplete code snippets: 5</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix snippet 15: missing or unknown language</li><li>Fix snippet 16: missing or unknown language</li><li>Fix snippet 17: missing or unknown language</li></ul></div></div></div><h5>正确性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>格式正确性</strong>
                            <span class='metric-score score-good'>80.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Format validation passed</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>format_issues:</strong> Invalid date format (expected: YYYY-MM-DD)</div><div><strong>issues_count:</strong> 1</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix format issue: Invalid date format (expected: YYYY-MM-DD)</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>logical_consistency</strong>
                            <span class='metric-score score-good'>85.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Content is logically consistent</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>consistency_issues:</strong> Business and code tags are inconsistent</div><div><strong>issues_count:</strong> 1</div><div><strong>steps_length:</strong> 69</div><div><strong>results_length:</strong> 53</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Business and code tags are inconsistent</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>data_validity</strong>
                            <span class='metric-score score-poor'>0.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Critical data validity issues</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>validity_issues:</strong> Date format is invalid, Code snippet 16 has unknown language, Code snippet 17 has unknown language ... (共7项)</div><div><strong>duplicate_steps:</strong> 8, 25, 28 ... (共25项)</div><div><strong>issues_count:</strong> 7</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Date format is invalid</li><li>Fix: Code snippet 16 has unknown language</li><li>Fix: Code snippet 17 has unknown language</li><li>... 还有2个建议</li></ul></div></div></div><h5>难度</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>technical_complexity</strong>
                            <span class='metric-score score-poor'>30.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Low technical complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>complexity_factors:</strong> Complex code patterns detected</div><div><strong>code_complexity:</strong> 30</div><div><strong>steps_complexity:</strong> 0</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>business_complexity</strong>
                            <span class='metric-score score-poor'>18.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Low business complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>scenario_complexity:</strong> 5</div><div><strong>integration_complexity:</strong> 13</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_coverage_complexity</strong>
                            <span class='metric-score score-poor'>18.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Limited test coverage</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>coverage_factors:</strong> Test types: negative, Edge cases: 1</div><div><strong>test_types:</strong> negative</div><div><strong>test_scenarios:</strong> 0</div><div><strong>edge_cases:</strong> 错误</div><div><strong>data_variations:</strong> 0</div></div></div></div></div>
                </td>
            </tr>
            <tr class="expandable" onclick="toggleDetails(this)">
                <td><span class="toggle-icon">▶</span> RAN-6808752</td>
                <td>测试用例 RAN-6808752_ UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验.md</td>
                <td><span class="metric-score score-fair">60.6</span></td>
                <td>10</td>
            </tr>
            <tr>
                <td colspan="4" class="details">
                    <div style='padding: 15px;'><h5>完整性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>必填字段</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>All required fields are present</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>present_fields:</strong> 9</div><div><strong>total_fields:</strong> 9</div><div><strong>completion_rate:</strong> 9/9</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>content_structure</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Content structure is complete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>issues_count:</strong> 0</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_steps_completeness</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Test steps are complete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>steps_length:</strong> 56</div><div><strong>expected_results_length:</strong> 101</div><div><strong>content_ratio:</strong> 0.5544554455445545</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>code_snippets_completeness</strong>
                            <span class='metric-score score-good'>80.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Code snippets are complete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>snippets_count:</strong> 15</div><div><strong>incomplete_snippets:</strong> {'index': 13, 'issues': ['missing or unknown language']}, {'index': 14, 'issues': ['missing or unknown language']}</div><div><strong>issues:</strong> Incomplete code snippets: 2</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix snippet 13: missing or unknown language</li><li>Fix snippet 14: missing or unknown language</li></ul></div></div></div><h5>正确性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>格式正确性</strong>
                            <span class='metric-score score-good'>80.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Format validation passed</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>format_issues:</strong> Invalid date format (expected: YYYY-MM-DD)</div><div><strong>issues_count:</strong> 1</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix format issue: Invalid date format (expected: YYYY-MM-DD)</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>logical_consistency</strong>
                            <span class='metric-score score-good'>85.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Content is logically consistent</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>consistency_issues:</strong> Business and code tags are inconsistent</div><div><strong>issues_count:</strong> 1</div><div><strong>steps_length:</strong> 56</div><div><strong>results_length:</strong> 101</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Business and code tags are inconsistent</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>data_validity</strong>
                            <span class='metric-score score-poor'>0.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Critical data validity issues</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>validity_issues:</strong> Date format is invalid, Code snippet 14 has unknown language, Code snippet 15 has unknown language ... (共4项)</div><div><strong>duplicate_steps:</strong> 12, 17, 20 ... (共26项)</div><div><strong>issues_count:</strong> 4</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Date format is invalid</li><li>Fix: Code snippet 14 has unknown language</li><li>Fix: Code snippet 15 has unknown language</li><li>... 还有1个建议</li></ul></div></div></div><h5>难度</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>technical_complexity</strong>
                            <span class='metric-score score-poor'>30.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Low technical complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>complexity_factors:</strong> Complex code patterns detected</div><div><strong>code_complexity:</strong> 30</div><div><strong>steps_complexity:</strong> 0</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>business_complexity</strong>
                            <span class='metric-score score-poor'>13.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Low business complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>scenario_complexity:</strong> 0</div><div><strong>integration_complexity:</strong> 13</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_coverage_complexity</strong>
                            <span class='metric-score score-poor'>18.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Limited test coverage</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>coverage_factors:</strong> Test types: negative, Edge cases: 1</div><div><strong>test_types:</strong> negative</div><div><strong>test_scenarios:</strong> 0</div><div><strong>edge_cases:</strong> 失败</div><div><strong>data_variations:</strong> 0</div></div></div></div></div>
                </td>
            </tr>
            <tr class="expandable" onclick="toggleDetails(this)">
                <td><span class="toggle-icon">▶</span> RAN-6612580</td>
                <td>测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md</td>
                <td><span class="metric-score score-poor">56.9</span></td>
                <td>10</td>
            </tr>
            <tr>
                <td colspan="4" class="details">
                    <div style='padding: 15px;'><h5>完整性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>必填字段</strong>
                            <span class='metric-score score-excellent'>100.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>All required fields are present</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>present_fields:</strong> 9</div><div><strong>total_fields:</strong> 9</div><div><strong>completion_rate:</strong> 9/9</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>content_structure</strong>
                            <span class='metric-score score-poor'>50.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Critical structure issues found: 2</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>structure_issues:</strong> Test steps are missing or too short, Expected results are missing or too short</div><div><strong>issues_count:</strong> 2</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Test steps are missing or too short</li><li>Fix: Expected results are missing or too short</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_steps_completeness</strong>
                            <span class='metric-score score-poor'>40.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Test steps are incomplete</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>steps_length:</strong> 3</div><div><strong>expected_results_length:</strong> 3</div><div><strong>content_ratio:</strong> 1.0</div><div><strong>issues:</strong> Test steps content too short: 3 chars (minimum: 20), Expected results content too short: 3 chars (minimum: 20)</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Expand test steps content (current: 3 chars, minimum: 20)</li><li>Expand expected results content (current: 3 chars, minimum: 20)</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>code_snippets_completeness</strong>
                            <span class='metric-score score-fair'>70.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Code snippets need improvement</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>snippets_count:</strong> 28</div><div><strong>incomplete_snippets:</strong> {'index': 16, 'issues': ['missing or unknown language']}, {'index': 17, 'issues': ['missing or unknown language']}, {'index': 18, 'issues': ['missing or unknown language']} ... (共12项)</div><div><strong>issues:</strong> Incomplete code snippets: 12</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix snippet 16: missing or unknown language</li><li>Fix snippet 17: missing or unknown language</li><li>Fix snippet 18: missing or unknown language</li></ul></div></div></div><h5>正确性</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>格式正确性</strong>
                            <span class='metric-score score-fair'>75.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Format issues found: 1</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>format_issues:</strong> Invalid Gerrit link format</div><div><strong>issues_count:</strong> 1</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix format issue: Invalid Gerrit link format</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>logical_consistency</strong>
                            <span class='metric-score score-good'>85.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Content is logically consistent</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>consistency_issues:</strong> Business and code tags are inconsistent</div><div><strong>issues_count:</strong> 1</div><div><strong>steps_length:</strong> 3</div><div><strong>results_length:</strong> 3</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: Business and code tags are inconsistent</li></ul></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>data_validity</strong>
                            <span class='metric-score score-poor'>20.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Critical data validity issues</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>validity_issues:</strong> TC steps content is too short, TC expected results content is too short, Code snippet 17 has unknown language ... (共14项)</div><div><strong>issues_count:</strong> 14</div></div><div style='margin: 5px 0; font-size: 0.9em;'><strong>建议:</strong><ul style='margin: 5px 0; padding-left: 20px;'><li>Fix: TC steps content is too short</li><li>Fix: TC expected results content is too short</li><li>Fix: Code snippet 17 has unknown language</li><li>... 还有2个建议</li></ul></div></div></div><h5>难度</h5><div style='margin-left: 20px; margin-bottom: 15px;'>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>technical_complexity</strong>
                            <span class='metric-score score-poor'>30.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Low technical complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>complexity_factors:</strong> Complex code patterns detected</div><div><strong>code_complexity:</strong> 30</div><div><strong>steps_complexity:</strong> 0</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>business_complexity</strong>
                            <span class='metric-score score-fair'>71.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>High business complexity</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> High</div><div><strong>business_factors:</strong> Business domains: ['telecom', 'system']</div><div><strong>scenario_complexity:</strong> 5</div><div><strong>integration_complexity:</strong> 10</div></div></div>
                    <div style='margin: 10px 0; padding: 10px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;'>
                        <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;'>
                            <strong>test_coverage_complexity</strong>
                            <span class='metric-score score-poor'>28.0</span>
                        </div>
                        <div style='margin: 5px 0; color: #666;'>Limited test coverage</div><div style='margin: 5px 0; font-size: 0.9em;'><div><strong>difficulty_level:</strong> Low</div><div><strong>coverage_factors:</strong> Test types: functional, negative, Edge cases: 1</div><div><strong>test_types:</strong> functional, negative</div><div><strong>test_scenarios:</strong> 0</div><div><strong>edge_cases:</strong> 失败</div><div><strong>data_variations:</strong> 0</div></div></div></div></div>
                </td>
            </tr>
                        </tbody>
                    </table>
                </div>
                
            </div>
        </div>
        
        
        <div class="footer">
            <p>报告由 FT语料评估系统 自动生成 | 生成时间: 2025-07-25 15:57:59</p>
        </div>
    </div>
</body>
</html>