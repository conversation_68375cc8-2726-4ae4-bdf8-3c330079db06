<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT语料综合评估报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .summary-card:hover {
            transform: translateY(-5px);
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .summary-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-card .description {
            color: #888;
            font-size: 0.9em;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .dimension-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .dimension-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dimension-title {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .dimension-score {
            font-size: 1.2em;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
        }
        
        .metric-list {
            padding: 20px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-name {
            font-weight: 500;
        }
        
        .metric-score {
            font-weight: bold;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        
        .score-excellent { background-color: #28a745; }
        .score-good { background-color: #17a2b8; }
        .score-fair { background-color: #ffc107; color: #333; }
        .score-poor { background-color: #dc3545; }
        
        .level-critical { background-color: #dc3545; }
        .level-warning { background-color: #ffc107; color: #333; }
        .level-info { background-color: #17a2b8; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .expandable {
            cursor: pointer;
            user-select: none;
        }
        
        .expandable:hover {
            background-color: #f8f9fa;
        }
        
        .details {
            display: none;
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .details.show {
            display: block;
        }
        
        .tag {
            display: inline-block;
            padding: 3px 8px;
            background-color: #e9ecef;
            color: #495057;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }
        
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .recommendations h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .recommendations li {
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
        }
        
        .recommendations li:last-child {
            border-bottom: none;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .dimension-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        </style>
    <script>
        function toggleDetails(element) {
            const details = element.nextElementSibling;
            details.classList.toggle('show');
            
            const icon = element.querySelector('.toggle-icon');
            if (icon) {
                icon.textContent = details.classList.contains('show') ? '▼' : '▶';
            }
        }
        
        function getScoreClass(score) {
            if (score >= 90) return 'score-excellent';
            if (score >= 80) return 'score-good';
            if (score >= 60) return 'score-fair';
            return 'score-poor';
        }
        
        function getLevelClass(level) {
            switch(level) {
                case 'critical': return 'level-critical';
                case 'warning': return 'level-warning';
                case 'info': return 'level-info';
                default: return 'level-info';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化进度条动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });
        });
        </script>
</head>
<body>
    <div class="container">
        
        <div class="header">
            <h1>FT语料综合评估报告</h1>
            <div class="subtitle">
                生成时间: 2025-07-25 15:34:56 |
                总语料数: 0 |
                评估维度: 0
            </div>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>总体评分</h3>
                <div class="value score-poor">0.0</div>
                <div class="description">平均分数</div>
            </div>
            <div class="summary-card">
                <h3>语料总数</h3>
                <div class="value">0</div>
                <div class="description">已评估语料</div>
            </div>
            <div class="summary-card">
                <h3>失败数量</h3>
                <div class="value score-poor">0</div>
                <div class="description">处理失败</div>
            </div>
            <div class="summary-card">
                <h3>优秀率</h3>
                <div class="value score-excellent">0.0%</div>
                <div class="description">90分以上</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>📊 维度评估概览</h2>
            </div>
            <div class="section-content">
                
            </div>
        </div>
        
        
        
        
        <div class="footer">
            <p>报告由 FT语料评估系统 自动生成 | 生成时间: 2025-07-25 15:34:56</p>
        </div>
    </div>
</body>
</html>