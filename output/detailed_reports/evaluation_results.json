{"summary": {"total_files": 4, "average_score": 0.0, "score_distribution": {"excellent": 0, "good": 0, "fair": 0, "poor": 4}, "issue_counts": {"critical": 4}, "top_issues": [{"issue": "dimension_evaluator: <PERSON><PERSON><PERSON> failed: 'CompletenessDimension' object has no attribute 'evaluate'", "frequency": 4}]}, "reports": [{"corpus_id": "RAN-6808752", "file_path": "/home/<USER>/Works/AICoP/FT/ft_corpus_eval/corpus/测试用例 RAN-6808752_ UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验.md", "overall_score": 0, "evaluation_results": [{"evaluator_name": "dimension_evaluator", "level": "critical", "score": 0, "message": "Evaluator failed: 'CompletenessDimension' object has no attribute 'evaluate'", "details": {"error": "'CompletenessDimension' object has no attribute 'evaluate'"}, "suggestions": null}], "metadata": {"evaluator_count": 1, "total_issues": 1}}, {"corpus_id": "RAN-5946243", "file_path": "/home/<USER>/Works/AICoP/FT/ft_corpus_eval/corpus/RAN-5946243 RRC建立优化场景MSG5的传递路径：LUCM-_LUC（UlDcchTranmissionMessage）传递远近点标识.md", "overall_score": 0, "evaluation_results": [{"evaluator_name": "dimension_evaluator", "level": "critical", "score": 0, "message": "Evaluator failed: 'CompletenessDimension' object has no attribute 'evaluate'", "details": {"error": "'CompletenessDimension' object has no attribute 'evaluate'"}, "suggestions": null}], "metadata": {"evaluator_count": 1, "total_issues": 1}}, {"corpus_id": "RAN-5869391", "file_path": "/home/<USER>/Works/AICoP/FT/ft_corpus_eval/corpus/RAN-5869391.md", "overall_score": 0, "evaluation_results": [{"evaluator_name": "dimension_evaluator", "level": "critical", "score": 0, "message": "Evaluator failed: 'CompletenessDimension' object has no attribute 'evaluate'", "details": {"error": "'CompletenessDimension' object has no attribute 'evaluate'"}, "suggestions": null}], "metadata": {"evaluator_count": 1, "total_issues": 1}}, {"corpus_id": "RAN-6612580", "file_path": "/home/<USER>/Works/AICoP/FT/ft_corpus_eval/corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md", "overall_score": 0, "evaluation_results": [{"evaluator_name": "dimension_evaluator", "level": "critical", "score": 0, "message": "Evaluator failed: 'CompletenessDimension' object has no attribute 'evaluate'", "details": {"error": "'CompletenessDimension' object has no attribute 'evaluate'"}, "suggestions": null}], "metadata": {"evaluator_count": 1, "total_issues": 1}}], "failed_files": [], "total_processed": 4, "total_failed": 0}