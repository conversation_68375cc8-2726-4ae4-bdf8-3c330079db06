# FT语料评估HTML报告生成器

## 概述

HTML报告生成器提供了详细、美观的评估报告，包含丰富的可视化元素和交互功能。

## 功能特性

### 📊 详细的评估报告
- **综合概览**: 总体评分、语料统计、维度分析
- **维度详情**: 每个评估维度的详细分析和指标
- **语料详情**: 单个语料的详细评估结果
- **改进建议**: 基于评估结果的具体改进建议

### 🎨 美观的界面设计
- **响应式设计**: 支持桌面和移动设备
- **现代化UI**: 使用渐变色彩和卡片式布局
- **交互元素**: 可展开的详情、进度条、悬停效果
- **颜色编码**: 根据评分等级使用不同颜色

### 📈 数据可视化
- **评分分布**: 优秀、良好、一般、较差的分布统计
- **进度条**: 直观显示各指标的评分情况
- **统计卡片**: 关键指标的突出显示
- **趋势分析**: 整体质量趋势展示

## 使用方法

### 基本用法

```python
from ft_corpus_evaluator.reports.html_report_generator import HTMLReportGenerator

# 创建报告生成器
html_generator = HTMLReportGenerator()

# 生成详细报告
html_generator.generate_detailed_report(
    evaluation_result=result,  # 评估结果数据
    output_path=Path("report.html"),  # 输出文件路径
    title="我的评估报告"  # 报告标题
)
```

### 与批处理器集成

```python
from ft_corpus_evaluator.core.batch_processor import BatchProcessor
from ft_corpus_evaluator.core.evaluator_manager import EvaluatorManager

# 初始化
evaluator_manager = EvaluatorManager()
batch_processor = BatchProcessor(evaluator_manager)

# 处理语料并生成报告
result = batch_processor.process_directory(
    corpus_dir=Path("corpus"),
    output_dir=Path("output")
)
# 会自动生成 detailed_evaluation_report.html
```

### 命令行使用

```bash
# 生成实际语料的详细报告
python examples/generate_detailed_report.py

# 生成示例报告（用于演示）
python examples/generate_detailed_report.py --sample
```

## 报告结构

### 1. 报告头部
- 报告标题和生成时间
- 基本统计信息（语料数量、评估维度等）

### 2. 概览摘要
- 总体评分卡片
- 语料统计信息
- 质量分布情况
- 关键指标概览

### 3. 维度分析
- 每个评估维度的详细分析
- 维度内各指标的评分和说明
- 进度条显示评分情况
- 具体的改进建议

### 4. 语料详情
- 可展开的语料列表
- 每个语料的详细评估结果
- 按维度组织的指标详情
- 问题和建议的具体说明

### 5. 改进建议
- 汇总的改进建议
- 按优先级排序的建议列表
- 具体的操作指导

## 自定义配置

### 样式自定义

可以通过修改 `_get_css_styles()` 方法来自定义报告样式：

```python
class CustomHTMLReportGenerator(HTMLReportGenerator):
    def _get_css_styles(self) -> str:
        # 返回自定义的CSS样式
        return """<style>
        /* 自定义样式 */
        .header {
            background: linear-gradient(135deg, #your-color1, #your-color2);
        }
        </style>"""
```

### 内容自定义

可以重写相关方法来自定义报告内容：

```python
def _generate_custom_section(self, result: Dict[str, Any]) -> str:
    # 生成自定义章节
    return "<div>自定义内容</div>"
```

## 输出文件

生成的HTML报告包含以下文件：

1. **detailed_evaluation_report.html** - 详细评估报告
2. **evaluation_report.html** - 简化版报告（向后兼容）
3. **evaluation_results.json** - 原始JSON数据

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 技术特性

- **纯HTML/CSS/JS**: 无需额外依赖
- **离线可用**: 所有资源内嵌在HTML中
- **打印友好**: 支持打印和PDF导出
- **无障碍支持**: 符合基本的无障碍访问标准

## 示例报告

运行以下命令查看示例报告：

```bash
python examples/generate_detailed_report.py --sample
```

这将生成一个包含示例数据的详细HTML报告，展示所有功能特性。

## 故障排除

### 常见问题

1. **报告无法打开**
   - 检查文件路径是否正确
   - 确保浏览器支持本地文件访问

2. **样式显示异常**
   - 检查HTML文件是否完整
   - 尝试在不同浏览器中打开

3. **数据显示不完整**
   - 检查输入的评估结果数据格式
   - 确保所有必需的字段都存在

### 调试模式

可以通过添加调试信息来排查问题：

```python
html_generator = HTMLReportGenerator()
# 在生成前检查数据
print(json.dumps(result, indent=2))
html_generator.generate_detailed_report(result, output_path)
```
