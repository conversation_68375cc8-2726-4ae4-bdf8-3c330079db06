from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
import json
from ..models.evaluation_models import EvaluationLevel, EvaluationDimension


class HTMLReportGenerator:
    def __init__(self):
        self.template_dir = Path(__file__).parent / "templates"
        
    def generate_detailed_report(self, evaluation_result: Dict[str, Any], 
                               output_path: Path, 
                               title: str = "FT语料评估详细报告") -> None:
        html_content = self._build_html_report(evaluation_result, title)
        output_path.write_text(html_content, encoding="utf-8")
        
    def _build_html_report(self, result: Dict[str, Any], title: str) -> str:
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    {self._get_css_styles()}
    {self._get_javascript()}
</head>
<body>
    <div class="container">
        {self._generate_header(title, result)}
        {self._generate_summary_section(result)}
        {self._generate_dimension_overview(result)}
        {self._generate_detailed_results(result)}
        {self._generate_corpus_details(result)}
        {self._generate_recommendations(result)}
        {self._generate_footer()}
    </div>
</body>
</html>"""

    def _get_css_styles(self) -> str:
        return """<style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .summary-card:hover {
            transform: translateY(-5px);
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .summary-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-card .description {
            color: #888;
            font-size: 0.9em;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .dimension-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .dimension-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dimension-title {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .dimension-score {
            font-size: 1.2em;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
        }
        
        .metric-list {
            padding: 20px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-name {
            font-weight: 500;
        }
        
        .metric-score {
            font-weight: bold;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        
        .score-excellent { background-color: #28a745; }
        .score-good { background-color: #17a2b8; }
        .score-fair { background-color: #ffc107; color: #333; }
        .score-poor { background-color: #dc3545; }
        
        .level-critical { background-color: #dc3545; }
        .level-warning { background-color: #ffc107; color: #333; }
        .level-info { background-color: #17a2b8; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .expandable {
            cursor: pointer;
            user-select: none;
        }
        
        .expandable:hover {
            background-color: #f8f9fa;
        }
        
        .details {
            display: none;
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .details.show {
            display: block;
        }
        
        .tag {
            display: inline-block;
            padding: 3px 8px;
            background-color: #e9ecef;
            color: #495057;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }
        
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .recommendations h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .recommendations li {
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
        }
        
        .recommendations li:last-child {
            border-bottom: none;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .dimension-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        </style>"""

    def _get_javascript(self) -> str:
        return """<script>
        function toggleDetails(element) {
            const details = element.nextElementSibling;
            details.classList.toggle('show');
            
            const icon = element.querySelector('.toggle-icon');
            if (icon) {
                icon.textContent = details.classList.contains('show') ? '▼' : '▶';
            }
        }
        
        function getScoreClass(score) {
            if (score >= 90) return 'score-excellent';
            if (score >= 80) return 'score-good';
            if (score >= 60) return 'score-fair';
            return 'score-poor';
        }
        
        function getLevelClass(level) {
            switch(level) {
                case 'critical': return 'level-critical';
                case 'warning': return 'level-warning';
                case 'info': return 'level-info';
                default: return 'level-info';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化进度条动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });
        });
        </script>"""

    def _generate_header(self, title: str, result: Dict[str, Any]) -> str:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        summary = result.get('summary', {})

        return f"""
        <div class="header">
            <h1>{title}</h1>
            <div class="subtitle">
                生成时间: {timestamp} |
                总语料数: {summary.get('total_corpus_count', 0)} |
                评估维度: {len(summary.get('dimensions_evaluated', []))}
            </div>
        </div>"""

    def _generate_summary_section(self, result: Dict[str, Any]) -> str:
        summary = result.get('summary', {})
        collection_results = result.get('collection_results', {})

        # 计算总体评分
        total_score = 0
        dimension_count = 0
        for dim_results in collection_results.values():
            for metric_result in dim_results.values():
                if isinstance(metric_result, dict) and 'score' in metric_result:
                    total_score += metric_result['score']
                    dimension_count += 1

        avg_score = total_score / dimension_count if dimension_count > 0 else 0

        # 计算分布统计
        score_distribution = self._calculate_score_distribution(result)

        return f"""
        <div class="summary-grid">
            <div class="summary-card">
                <h3>总体评分</h3>
                <div class="value {self._get_score_class(avg_score)}">{avg_score:.1f}</div>
                <div class="description">平均分数</div>
            </div>
            <div class="summary-card">
                <h3>语料总数</h3>
                <div class="value">{summary.get('total_corpus_count', 0)}</div>
                <div class="description">已评估语料</div>
            </div>
            <div class="summary-card">
                <h3>失败数量</h3>
                <div class="value score-poor">{summary.get('total_failed_count', 0)}</div>
                <div class="description">处理失败</div>
            </div>
            <div class="summary-card">
                <h3>优秀率</h3>
                <div class="value score-excellent">{score_distribution.get('excellent_rate', 0):.1f}%</div>
                <div class="description">90分以上</div>
            </div>
        </div>"""

    def _generate_dimension_overview(self, result: Dict[str, Any]) -> str:
        collection_results = result.get('collection_results', {})

        dimension_cards = ""
        for dim_name, dim_results in collection_results.items():
            if not dim_results:
                continue

            # 计算维度平均分
            scores = []
            for metric_result in dim_results.values():
                if isinstance(metric_result, dict) and 'score' in metric_result:
                    scores.append(metric_result['score'])

            avg_score = sum(scores) / len(scores) if scores else 0

            dimension_cards += f"""
            <div class="dimension-card">
                <div class="dimension-header">
                    <div class="dimension-title">{self._get_dimension_display_name(dim_name)}</div>
                    <div class="dimension-score {self._get_score_class(avg_score)}">{avg_score:.1f}</div>
                </div>
                <div class="metric-list">
                    {self._generate_metric_items(dim_results)}
                </div>
            </div>"""

        return f"""
        <div class="section">
            <div class="section-header">
                <h2>📊 维度评估概览</h2>
            </div>
            <div class="section-content">
                {dimension_cards}
            </div>
        </div>"""

    def _generate_detailed_results(self, result: Dict[str, Any]) -> str:
        collection_results = result.get('collection_results', {})

        detailed_sections = ""
        for dim_name, dim_results in collection_results.items():
            if not dim_results:
                continue

            detailed_sections += f"""
            <div class="section">
                <div class="section-header">
                    <h2>📈 {self._get_dimension_display_name(dim_name)} - 详细分析</h2>
                </div>
                <div class="section-content">
                    {self._generate_dimension_details(dim_results)}
                </div>
            </div>"""

        return detailed_sections

    def _generate_corpus_details(self, result: Dict[str, Any]) -> str:
        corpus_results = result.get('corpus_results', [])

        if not corpus_results:
            return ""

        # 生成语料详情表格
        table_rows = ""
        for i, corpus_result in enumerate(corpus_results[:50]):  # 限制显示前50个
            corpus_id = corpus_result.get('corpus_id', f'语料{i+1}')
            file_path = Path(corpus_result.get('file_path', '')).name

            # 计算语料总分
            total_score = 0
            metric_count = 0
            for dim_results in corpus_result.get('results', {}).values():
                for metric_result in dim_results.values():
                    if hasattr(metric_result, 'score'):
                        total_score += metric_result.score
                        metric_count += 1

            avg_score = total_score / metric_count if metric_count > 0 else 0

            table_rows += f"""
            <tr class="expandable" onclick="toggleDetails(this)">
                <td><span class="toggle-icon">▶</span> {corpus_id}</td>
                <td>{file_path}</td>
                <td><span class="metric-score {self._get_score_class(avg_score)}">{avg_score:.1f}</span></td>
                <td>{metric_count}</td>
            </tr>
            <tr>
                <td colspan="4" class="details">
                    {self._generate_corpus_detail_content(corpus_result)}
                </td>
            </tr>"""

        return f"""
        <div class="section">
            <div class="section-header">
                <h2>📋 语料详细信息</h2>
            </div>
            <div class="section-content">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>语料ID</th>
                                <th>文件名</th>
                                <th>总分</th>
                                <th>指标数</th>
                            </tr>
                        </thead>
                        <tbody>
                            {table_rows}
                        </tbody>
                    </table>
                </div>
                {f'<p style="margin-top: 15px; color: #666;">显示前50个语料，共{len(corpus_results)}个</p>' if len(corpus_results) > 50 else ''}
            </div>
        </div>"""

    def _generate_recommendations(self, result: Dict[str, Any]) -> str:
        recommendations = []
        collection_results = result.get('collection_results', {})

        # 从各维度收集建议
        for dim_results in collection_results.values():
            for metric_result in dim_results.values():
                if isinstance(metric_result, dict) and metric_result.get('suggestions'):
                    recommendations.extend(metric_result['suggestions'])

        if not recommendations:
            return ""

        # 去重并限制数量
        unique_recommendations = list(dict.fromkeys(recommendations))[:10]

        recommendation_items = ""
        for rec in unique_recommendations:
            recommendation_items += f"<li>💡 {rec}</li>"

        return f"""
        <div class="recommendations">
            <h3>🎯 改进建议</h3>
            <ul>
                {recommendation_items}
            </ul>
        </div>"""

    def _generate_footer(self) -> str:
        return f"""
        <div class="footer">
            <p>报告由 FT语料评估系统 自动生成 | 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        </div>"""

    def _calculate_score_distribution(self, result: Dict[str, Any]) -> Dict[str, float]:
        corpus_results = result.get('corpus_results', [])
        if not corpus_results:
            return {'excellent_rate': 0, 'good_rate': 0, 'fair_rate': 0, 'poor_rate': 0}

        excellent = good = fair = poor = 0

        for corpus_result in corpus_results:
            # 计算每个语料的平均分
            total_score = 0
            metric_count = 0

            for dim_results in corpus_result.get('results', {}).values():
                for metric_result in dim_results.values():
                    if hasattr(metric_result, 'score'):
                        total_score += metric_result.score
                        metric_count += 1

            if metric_count > 0:
                avg_score = total_score / metric_count
                if avg_score >= 90:
                    excellent += 1
                elif avg_score >= 80:
                    good += 1
                elif avg_score >= 60:
                    fair += 1
                else:
                    poor += 1

        total = len(corpus_results)
        return {
            'excellent_rate': (excellent / total * 100) if total > 0 else 0,
            'good_rate': (good / total * 100) if total > 0 else 0,
            'fair_rate': (fair / total * 100) if total > 0 else 0,
            'poor_rate': (poor / total * 100) if total > 0 else 0
        }

    def _get_score_class(self, score: float) -> str:
        if score >= 90:
            return 'score-excellent'
        elif score >= 80:
            return 'score-good'
        elif score >= 60:
            return 'score-fair'
        else:
            return 'score-poor'

    def _get_dimension_display_name(self, dim_name: str) -> str:
        display_names = {
            'completeness': '完整性',
            'correctness': '正确性',
            'difficulty': '难度'
        }
        return display_names.get(dim_name, dim_name)

    def _generate_metric_items(self, dim_results: Dict[str, Any]) -> str:
        items = ""
        for metric_name, metric_result in dim_results.items():
            if isinstance(metric_result, dict):
                score = metric_result.get('score', 0)
                level = metric_result.get('level', 'info')

                items += f"""
                <div class="metric-item">
                    <div class="metric-name">{self._get_metric_display_name(metric_name)}</div>
                    <div class="metric-score {self._get_score_class(score)}">{score:.1f}</div>
                </div>"""

        return items

    def _get_metric_display_name(self, metric_name: str) -> str:
        display_names = {
            'required_fields': '必填字段',
            'content_length': '内容长度',
            'format_correctness': '格式正确性',
            'data_consistency': '数据一致性',
            'complexity_analysis': '复杂度分析',
            'technical_depth': '技术深度'
        }
        return display_names.get(metric_name, metric_name)

    def _generate_dimension_details(self, dim_results: Dict[str, Any]) -> str:
        details = ""
        for metric_name, metric_result in dim_results.items():
            if not isinstance(metric_result, dict):
                continue

            score = metric_result.get('score', 0)
            message = metric_result.get('message', '')
            level = metric_result.get('level', 'info')
            metric_details = metric_result.get('details', {})
            suggestions = metric_result.get('suggestions', [])

            # 生成进度条
            progress_bar = f"""
            <div class="progress-bar">
                <div class="progress-fill {self._get_score_class(score)}" style="width: {score}%"></div>
            </div>"""

            # 生成详细信息
            detail_items = ""
            for key, value in metric_details.items():
                if isinstance(value, list):
                    value = f"{len(value)} 项"
                detail_items += f"<div><strong>{key}:</strong> {value}</div>"

            # 生成建议
            suggestion_items = ""
            for suggestion in suggestions[:3]:  # 限制显示3个建议
                suggestion_items += f"<div class='tag'>💡 {suggestion}</div>"

            details += f"""
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h4>{self._get_metric_display_name(metric_name)}</h4>
                {progress_bar}
                <div style="margin: 10px 0;">
                    <span class="metric-score {self._get_level_class(level)}">{level.upper()}</span>
                    <span style="margin-left: 10px;">{message}</span>
                </div>
                {f'<div style="margin: 10px 0; font-size: 0.9em; color: #666;">{detail_items}</div>' if detail_items else ''}
                {f'<div style="margin: 10px 0;">{suggestion_items}</div>' if suggestion_items else ''}
            </div>"""

        return details

    def _get_level_class(self, level: str) -> str:
        level_classes = {
            'critical': 'level-critical',
            'warning': 'level-warning',
            'info': 'level-info'
        }
        return level_classes.get(level, 'level-info')

    def _generate_corpus_detail_content(self, corpus_result: Dict[str, Any]) -> str:
        content = "<div style='padding: 15px;'>"

        for dim_name, dim_results in corpus_result.get('results', {}).items():
            content += f"<h5>{self._get_dimension_display_name(dim_name)}</h5>"
            content += "<div style='margin-left: 20px; margin-bottom: 15px;'>"

            for metric_name, metric_result in dim_results.items():
                if hasattr(metric_result, 'score'):
                    score = metric_result.score
                    message = metric_result.message
                    level = metric_result.level.value

                    content += f"""
                    <div style='margin: 5px 0; display: flex; justify-content: space-between; align-items: center;'>
                        <span>{self._get_metric_display_name(metric_name)}: {message}</span>
                        <span class='metric-score {self._get_score_class(score)}'>{score:.1f}</span>
                    </div>"""

            content += "</div>"

        content += "</div>"
        return content
