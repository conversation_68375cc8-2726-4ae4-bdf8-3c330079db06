import re
from typing import List, Dict, Any
from .dimension_framework import BaseMetric, Dimension, CorpusMetric, CollectionMetric
from ..models.evaluation_models import EvaluationDimension, EvaluationLevel, MetricResult
from ..models.corpus_model import FTCorpus


class FormatCorrectnessMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("format_correctness", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        format_issues = []
        score = 100
        
        rdc_id_pattern = self.config.get('rdc_id_pattern', r'^RAN-\d+$')
        if not re.match(rdc_id_pattern, corpus.rdc_info.rdc_id or ''):
            format_issues.append("Invalid RDC ID format")
            score -= 25
        
        gerrit_pattern = self.config.get('gerrit_link_pattern', 
                                       r'^https://gerrit\.zte\.com\.cn/#/c/\d+(/\d+)?$')
        if not re.match(gerrit_pattern, corpus.rdc_info.gerrit_link or ''):
            format_issues.append("Invalid Gerrit link format")
            score -= 25
        
        date_pattern = self.config.get('date_pattern', r'^\d{4}-\d{2}-\d{2}$')
        if not re.match(date_pattern, corpus.rdc_info.date or ''):
            format_issues.append("Invalid date format (expected: YYYY-MM-DD)")
            score -= 20
        
        if corpus.rdc_info.repo_name and not corpus.rdc_info.repo_name.strip():
            format_issues.append("Repository name is empty")
            score -= 15
        
        invalid_tags = []
        for tag in corpus.tag_identification.business_content_scene_tags:
            if not tag or not tag.strip():
                invalid_tags.append("empty business tag")
        for tag in corpus.tag_identification.code_modify_scene_tags:
            if not tag or not tag.strip():
                invalid_tags.append("empty code modify tag")
        
        if invalid_tags:
            format_issues.append(f"Invalid tags: {len(invalid_tags)} empty tags")
            score -= min(15, len(invalid_tags) * 3)
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = f"Critical format errors: {len(format_issues)}"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = f"Format issues found: {len(format_issues)}"
        else:
            level = EvaluationLevel.INFO
            message = "Format validation passed"
        
        suggestions = [f"Fix format issue: {issue}" for issue in format_issues]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "format_issues": format_issues,
                "invalid_tags": invalid_tags,
                "issues_count": len(format_issues)
            },
            suggestions=suggestions
        )

    def collect_info(self, corpus: FTCorpus) -> Dict[str, Any]:
        """收集格式正确性相关信息"""
        format_issues = []

        rdc_id_pattern = self.config.get('rdc_id_pattern', r'^RAN-\d+$')
        if not re.match(rdc_id_pattern, corpus.rdc_info.rdc_id or ''):
            format_issues.append("rdc_id_format")

        gerrit_pattern = self.config.get('gerrit_link_pattern',
                                       r'^https://gerrit\.zte\.com\.cn/#/c/\d+(/\d+)?$')
        if not re.match(gerrit_pattern, corpus.rdc_info.gerrit_link or ''):
            format_issues.append("gerrit_link_format")

        date_pattern = self.config.get('date_pattern', r'^\d{4}-\d{2}-\d{2}$')
        if not re.match(date_pattern, corpus.rdc_info.date or ''):
            format_issues.append("date_format")

        return {
            "format_issues": format_issues,
            "has_format_issues": len(format_issues) > 0
        }

    def get_collection_info_keys(self) -> List[str]:
        return ["format_issues", "has_format_issues"]


class LogicalConsistencyMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("logical_consistency", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        consistency_issues = []
        score = 100
        
        steps_text = corpus.test_info.tc_steps
        expected_results_text = corpus.test_info.tc_expected_results

        # 简单检查：如果两个字段的长度差异很大，可能存在不一致
        steps_len = len(steps_text.strip())
        results_len = len(expected_results_text.strip())

        if steps_len == 0 and results_len > 0:
            consistency_issues.append("Missing test steps but expected results exist")
            score -= 30
        elif steps_len > 0 and results_len == 0:
            consistency_issues.append("Test steps exist but expected results are missing")
            score -= 30
        elif abs(steps_len - results_len) > max(steps_len, results_len) * 0.5:
            consistency_issues.append("Significant length mismatch between steps and expected results")
            score -= 15
        
        title_keywords = self._extract_keywords(corpus.test_info.test_title)
        steps_keywords = self._extract_keywords(steps_text)
        
        if not any(keyword in steps_keywords for keyword in title_keywords):
            consistency_issues.append("Test title doesn't match test content")
            score -= 20
        
        business_tags = corpus.tag_identification.business_content_scene_tags
        code_tags = corpus.tag_identification.code_modify_scene_tags
        
        if business_tags and code_tags:
            if not self._tags_are_consistent(business_tags, code_tags):
                consistency_issues.append("Business and code tags are inconsistent")
                score -= 15
        
        if corpus.code_snippets:
            code_languages = set(snippet.language for snippet in corpus.code_snippets)
            if len(code_languages) > 3:
                consistency_issues.append("Too many different programming languages")
                score -= 10
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Critical logical inconsistencies found"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Logical inconsistencies detected"
        else:
            level = EvaluationLevel.INFO
            message = "Content is logically consistent"
        
        suggestions = [f"Fix: {issue}" for issue in consistency_issues]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "consistency_issues": consistency_issues,
                "issues_count": len(consistency_issues),
                "steps_length": len(steps_text),
                "results_length": len(expected_results_text)
            },
            suggestions=suggestions
        )

    def collect_info(self, corpus: FTCorpus) -> Dict[str, Any]:
        """收集逻辑一致性相关信息"""
        consistency_issues = []

        steps_text = corpus.test_info.tc_steps
        expected_results_text = corpus.test_info.tc_expected_results

        steps_len = len(steps_text.strip())
        results_len = len(expected_results_text.strip())

        if steps_len == 0 and results_len > 0:
            consistency_issues.append("missing_steps")
        elif steps_len > 0 and results_len == 0:
            consistency_issues.append("missing_results")
        elif abs(steps_len - results_len) > max(steps_len, results_len) * 0.5:
            consistency_issues.append("length_mismatch")

        title_keywords = self._extract_keywords(corpus.test_info.test_title)
        steps_keywords = self._extract_keywords(steps_text)

        if not any(keyword in steps_keywords for keyword in title_keywords):
            consistency_issues.append("title_content_mismatch")

        return {
            "consistency_issues": consistency_issues,
            "has_consistency_issues": len(consistency_issues) > 0,
            "steps_length": steps_len,
            "results_length": results_len
        }

    def get_collection_info_keys(self) -> List[str]:
        return ["consistency_issues", "has_consistency_issues", "steps_length", "results_length"]
    
    def _has_action_result_mismatch(self, step: str, expected: str) -> bool:
        action_keywords = ['执行', '运行', '启动', '配置', '设置', '发送', '接收']
        result_keywords = ['成功', '失败', '显示', '输出', '返回', '收到']
        
        step_has_action = any(keyword in step for keyword in action_keywords)
        expected_has_result = any(keyword in expected for keyword in result_keywords)
        
        return step_has_action and not expected_has_result
    
    def _extract_keywords(self, text: str) -> List[str]:
        keywords = re.findall(r'[A-Za-z]+|[\u4e00-\u9fff]+', text)
        return [kw for kw in keywords if len(kw) > 1]
    
    def _tags_are_consistent(self, business_tags: List[str], code_tags: List[str]) -> bool:
        business_keywords = set()
        for tag in business_tags:
            business_keywords.update(self._extract_keywords(tag))
        
        code_keywords = set()
        for tag in code_tags:
            code_keywords.update(self._extract_keywords(tag))
        
        return len(business_keywords.intersection(code_keywords)) > 0


class DataValidityMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("data_validity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        validity_issues = []
        score = 100
        
        if corpus.rdc_info.rdc_id:
            rdc_number = re.search(r'RAN-(\d+)', corpus.rdc_info.rdc_id)
            if rdc_number and int(rdc_number.group(1)) < 1000000:
                validity_issues.append("RDC ID number seems too low")
                score -= 10
        
        if corpus.rdc_info.date:
            try:
                year = int(corpus.rdc_info.date.split('-')[0])
                if year < 2020 or year > 2030:
                    validity_issues.append("Date year is out of reasonable range")
                    score -= 15
            except (ValueError, IndexError):
                validity_issues.append("Date format is invalid")
                score -= 20
        
        # 检查TC步骤内容
        tc_steps = corpus.test_info.tc_steps.strip()
        if tc_steps:
            if len(tc_steps) < 10:
                validity_issues.append("TC steps content is too short")
                score -= 10
            elif len(tc_steps) > 5000:
                validity_issues.append("TC steps content is excessively long")
                score -= 5

        # 检查TC预期结果内容
        tc_results = corpus.test_info.tc_expected_results.strip()
        if tc_results:
            if len(tc_results) < 5:
                validity_issues.append("TC expected results content is too short")
                score -= 10
        
        for i, snippet in enumerate(corpus.code_snippets):
            if snippet.language == "unknown" and len(snippet.content) > 20:
                validity_issues.append(f"Code snippet {i+1} has unknown language")
                score -= 5
            
            if not snippet.file_path and len(snippet.content) > 50:
                validity_issues.append(f"Code snippet {i+1} missing file path")
                score -= 5
        
        duplicate_steps = self._find_duplicate_steps(corpus.test_info.tc_steps)
        if duplicate_steps:
            validity_issues.append(f"Duplicate test steps found: {len(duplicate_steps)}")
            score -= len(duplicate_steps) * 10
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Critical data validity issues"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Data validity issues detected"
        else:
            level = EvaluationLevel.INFO
            message = "Data is valid"
        
        suggestions = [f"Fix: {issue}" for issue in validity_issues[:5]]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "validity_issues": validity_issues,
                "duplicate_steps": duplicate_steps,
                "issues_count": len(validity_issues)
            },
            suggestions=suggestions
        )

    def collect_info(self, corpus: FTCorpus) -> Dict[str, Any]:
        """收集数据有效性相关信息"""
        validity_issues = []

        if corpus.rdc_info.rdc_id:
            rdc_number = re.search(r'RAN-(\d+)', corpus.rdc_info.rdc_id)
            if rdc_number and int(rdc_number.group(1)) < 1000000:
                validity_issues.append("low_rdc_number")

        if corpus.rdc_info.date:
            try:
                year = int(corpus.rdc_info.date.split('-')[0])
                if year < 2020 or year > 2030:
                    validity_issues.append("invalid_year")
            except (ValueError, IndexError):
                validity_issues.append("invalid_date_format")

        return {
            "validity_issues": validity_issues,
            "has_validity_issues": len(validity_issues) > 0
        }

    def get_collection_info_keys(self) -> List[str]:
        return ["validity_issues", "has_validity_issues"]
    
    def _find_duplicate_steps(self, steps: List[str]) -> List[int]:
        seen = {}
        duplicates = []
        for i, step in enumerate(steps):
            normalized = step.strip().lower()
            if normalized in seen:
                duplicates.append(i)
            else:
                seen[normalized] = i
        return duplicates


# 语料集指标
class FormatIssuesDistributionMetric(CollectionMetric):
    """格式问题分布统计指标"""

    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("format_issues_distribution", weight, enabled)

    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        all_format_issues = collected_info.get("format_issues", [])

        if not all_format_issues:
            return self.create_result(
                score=0,
                level=EvaluationLevel.CRITICAL,
                message="无法分析格式问题：缺少数据",
                dimension=EvaluationDimension.CORRECTNESS,
                details={"error": "No format issues data available"}
            )

        # 统计格式问题类型
        issue_types = {}
        total_corpus = len(all_format_issues)
        corpus_with_issues = 0

        for issues_list in all_format_issues:
            if issues_list:
                corpus_with_issues += 1
                for issue in issues_list:
                    if issue not in issue_types:
                        issue_types[issue] = 0
                    issue_types[issue] += 1

        # 计算格式正确率
        format_correctness_rate = (total_corpus - corpus_with_issues) / total_corpus * 100 if total_corpus > 0 else 0

        # 找出最常见的格式问题
        most_common_issues = sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:5]

        # 确定级别
        if format_correctness_rate >= 90:
            level = EvaluationLevel.INFO
            message = f"格式质量优秀，正确率 {format_correctness_rate:.1f}%"
        elif format_correctness_rate >= 70:
            level = EvaluationLevel.WARNING
            message = f"格式质量良好，正确率 {format_correctness_rate:.1f}%"
        else:
            level = EvaluationLevel.CRITICAL
            message = f"格式质量较差，正确率 {format_correctness_rate:.1f}%"

        return self.create_result(
            score=format_correctness_rate,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "total_corpus": total_corpus,
                "corpus_with_issues": corpus_with_issues,
                "format_correctness_rate": format_correctness_rate,
                "most_common_issues": most_common_issues,
                "issue_types_distribution": issue_types
            }
        )

    def required_info_keys(self) -> List[str]:
        return ["format_issues"]


class ConsistencyPatternAnalysisMetric(CollectionMetric):
    """一致性模式分析指标"""

    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("consistency_pattern_analysis", weight, enabled)

    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        all_consistency_issues = collected_info.get("consistency_issues", [])
        steps_lengths = collected_info.get("steps_length", [])
        results_lengths = collected_info.get("results_length", [])

        if not all_consistency_issues:
            return self.create_result(
                score=0,
                level=EvaluationLevel.CRITICAL,
                message="无法分析一致性模式：缺少数据",
                dimension=EvaluationDimension.CORRECTNESS,
                details={"error": "No consistency issues data available"}
            )

        # 统计一致性问题类型
        issue_types = {}
        total_corpus = len(all_consistency_issues)
        corpus_with_issues = 0

        for issues_list in all_consistency_issues:
            if issues_list:
                corpus_with_issues += 1
                for issue in issues_list:
                    if issue not in issue_types:
                        issue_types[issue] = 0
                    issue_types[issue] += 1

        # 计算一致性分数
        consistency_rate = (total_corpus - corpus_with_issues) / total_corpus * 100 if total_corpus > 0 else 0

        # 分析长度分布
        avg_steps_length = sum(steps_lengths) / len(steps_lengths) if steps_lengths else 0
        avg_results_length = sum(results_lengths) / len(results_lengths) if results_lengths else 0

        # 找出最常见的一致性问题
        most_common_issues = sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:5]

        # 确定级别
        if consistency_rate >= 85:
            level = EvaluationLevel.INFO
            message = f"逻辑一致性优秀，一致率 {consistency_rate:.1f}%"
        elif consistency_rate >= 70:
            level = EvaluationLevel.WARNING
            message = f"逻辑一致性良好，一致率 {consistency_rate:.1f}%"
        else:
            level = EvaluationLevel.CRITICAL
            message = f"逻辑一致性较差，一致率 {consistency_rate:.1f}%"

        return self.create_result(
            score=consistency_rate,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "total_corpus": total_corpus,
                "corpus_with_issues": corpus_with_issues,
                "consistency_rate": consistency_rate,
                "most_common_issues": most_common_issues,
                "avg_steps_length": avg_steps_length,
                "avg_results_length": avg_results_length,
                "issue_types_distribution": issue_types
            }
        )

    def required_info_keys(self) -> List[str]:
        return ["consistency_issues", "steps_length", "results_length"]


# 正确性维度
class CorrectnessDimension(Dimension):
    """正确性维度，支持单语料和语料集指标"""

    def __init__(self):
        super().__init__(EvaluationDimension.CORRECTNESS)

        # 注册单语料指标
        self.register_corpus_metric(FormatCorrectnessMetric())
        self.register_corpus_metric(LogicalConsistencyMetric())
        self.register_corpus_metric(DataValidityMetric())

        # 注册语料集指标
        self.register_collection_metric(FormatIssuesDistributionMetric())
        self.register_collection_metric(ConsistencyPatternAnalysisMetric())



