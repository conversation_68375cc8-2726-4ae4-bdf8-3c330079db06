from typing import Dict, Any, List
from .dimension_framework import CorpusMetric, CollectionMetric, Dimension
from ..models.evaluation_models import EvaluationDimension, EvaluationLevel, MetricResult
from ..models.corpus_model import FTCorpus


class RequiredFieldsMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("required_fields", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        required_fields = self.config.get('required_fields', {
            'rdc_info': ['rdc_id', 'repo_name', 'gerrit_link', 'date'],
            'test_info': ['test_title', 'tc_steps', 'tc_expected_results'],
            'tag_identification': ['business_content_scene_tags', 'code_modify_scene_tags']
        })
        
        missing_fields = []
        total_fields = 0
        present_fields = 0
        
        for section, fields in required_fields.items():
            section_obj = getattr(corpus, section, None)
            if section_obj is None:
                missing_fields.extend([f"{section}.{field}" for field in fields])
                total_fields += len(fields)
                continue
            
            for field in fields:
                total_fields += 1
                field_value = getattr(section_obj, field, None)
                if field_value is not None and field_value != "" and field_value != []:
                    present_fields += 1
                else:
                    missing_fields.append(f"{section}.{field}")
        
        score = (present_fields / total_fields * 100) if total_fields > 0 else 0
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = f"Critical: {len(missing_fields)} required fields missing"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = f"Warning: {len(missing_fields)} required fields missing"
        else:
            level = EvaluationLevel.INFO
            message = "All required fields are present"
        
        suggestions = []
        if missing_fields:
            suggestions = [f"Add missing field: {field}" for field in missing_fields[:5]]
            if len(missing_fields) > 5:
                suggestions.append(f"... and {len(missing_fields) - 5} more fields")
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "missing_fields": missing_fields,
                "present_fields": present_fields,
                "total_fields": total_fields,
                "completion_rate": f"{present_fields}/{total_fields}"
            },
            suggestions=suggestions
        )

    def collect_info(self, corpus: FTCorpus) -> Dict[str, Any]:
        """收集字段缺失信息供语料集指标使用"""
        # 使用与evaluate方法相同的逻辑
        required_fields = self.config.get('required_fields', {
            'rdc_info': ['rdc_id', 'repo_name', 'gerrit_link', 'date'],
            'test_info': ['test_title', 'tc_steps', 'tc_expected_results'],
            'tag_identification': ['business_content_scene_tags', 'code_modify_scene_tags']
        })

        missing_fields = []
        total_fields = 0
        present_fields = 0

        for section, fields in required_fields.items():
            section_obj = getattr(corpus, section, None)
            if section_obj is None:
                missing_fields.extend([f"{section}.{field}" for field in fields])
                total_fields += len(fields)
                continue

            for field in fields:
                total_fields += 1
                field_value = getattr(section_obj, field, None)
                if field_value is not None and field_value != "" and field_value != []:
                    present_fields += 1
                else:
                    missing_fields.append(f"{section}.{field}")

        completion_rate = (present_fields / total_fields) if total_fields > 0 else 1.0

        return {
            "missing_fields": missing_fields,
            "corpus_id": corpus.rdc_info.rdc_id,
            "field_completion_rate": completion_rate,
            "total_required_fields": total_fields,
            "present_fields": present_fields
        }

    def get_collection_info_keys(self) -> List[str]:
        return ["missing_fields", "corpus_id", "field_completion_rate", "total_required_fields", "present_fields"]


class ContentStructureMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("content_structure", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        structure_issues = []
        score = 100
        
        if not corpus.test_info.test_title or len(corpus.test_info.test_title.strip()) < 5:
            structure_issues.append("Test title is too short or missing")
            score -= 20
        
        if not corpus.test_info.tc_steps or len(corpus.test_info.tc_steps.strip()) < 10:
            structure_issues.append("Test steps are missing or too short")
            score -= 25
        
        if not corpus.test_info.tc_expected_results or len(corpus.test_info.tc_expected_results.strip()) < 5:
            structure_issues.append("Expected results are missing or too short")
            score -= 25
        
        if not corpus.tag_identification.business_content_scene_tags:
            structure_issues.append("Business content scene tags are missing")
            score -= 15
        
        if not corpus.tag_identification.code_modify_scene_tags:
            structure_issues.append("Code modify scene tags are missing")
            score -= 15
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = f"Critical structure issues found: {len(structure_issues)}"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = f"Structure issues found: {len(structure_issues)}"
        else:
            level = EvaluationLevel.INFO
            message = "Content structure is complete"
        
        suggestions = [f"Fix: {issue}" for issue in structure_issues]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "structure_issues": structure_issues,
                "issues_count": len(structure_issues)
            },
            suggestions=suggestions
        )


class TestStepsCompletenessMetric(CorpusMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("test_steps_completeness", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        min_content_length = self.config.get('min_step_length', 20)

        steps_text = corpus.test_info.tc_steps.strip()
        expected_results_text = corpus.test_info.tc_expected_results.strip()

        issues = []
        score = 100

        # 检查步骤内容长度
        if len(steps_text) < min_content_length:
            issues.append(f"Test steps content too short: {len(steps_text)} chars (minimum: {min_content_length})")
            score -= 30

        # 检查预期结果内容长度
        if len(expected_results_text) < min_content_length:
            issues.append(f"Expected results content too short: {len(expected_results_text)} chars (minimum: {min_content_length})")
            score -= 30

        # 检查内容平衡性
        if steps_text and expected_results_text:
            ratio = len(steps_text) / len(expected_results_text) if len(expected_results_text) > 0 else float('inf')
            if ratio > 5 or ratio < 0.2:
                issues.append(f"Imbalanced content: steps/results ratio is {ratio:.2f}")
                score -= 15
        
        score = max(0, score)

        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Test steps are incomplete"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Test steps need improvement"
        else:
            level = EvaluationLevel.INFO
            message = "Test steps are complete"

        suggestions = []
        if len(steps_text) < min_content_length:
            suggestions.append(f"Expand test steps content (current: {len(steps_text)} chars, minimum: {min_content_length})")
        if len(expected_results_text) < min_content_length:
            suggestions.append(f"Expand expected results content (current: {len(expected_results_text)} chars, minimum: {min_content_length})")
        if steps_text and expected_results_text:
            ratio = len(steps_text) / len(expected_results_text) if len(expected_results_text) > 0 else float('inf')
            if ratio > 5:
                suggestions.append("Expected results content seems too short compared to steps")
            elif ratio < 0.2:
                suggestions.append("Test steps content seems too short compared to expected results")
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "steps_length": len(steps_text),
                "expected_results_length": len(expected_results_text),
                "content_ratio": len(steps_text) / len(expected_results_text) if len(expected_results_text) > 0 else 0,
                "issues": issues
            },
            suggestions=suggestions
        )


class CodeSnippetsCompletenessMetric(CorpusMetric):
    def __init__(self, weight: float = 0.8, enabled: bool = True):
        super().__init__("code_snippets_completeness", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        min_snippets = self.config.get('min_snippets', 1)
        min_snippet_length = self.config.get('min_snippet_length', 50)
        
        snippets = corpus.code_snippets
        issues = []
        score = 100
        
        if len(snippets) < min_snippets:
            issues.append(f"Too few code snippets: {len(snippets)} (minimum: {min_snippets})")
            score -= 40
        
        incomplete_snippets = []
        for i, snippet in enumerate(snippets):
            snippet_issues = []
            if not snippet.file_path or not snippet.file_path.strip():
                snippet_issues.append("missing file path")
            if not snippet.language or snippet.language == "unknown":
                snippet_issues.append("missing or unknown language")
            if len(snippet.content) < min_snippet_length:
                snippet_issues.append("content too short")
            
            if snippet_issues:
                incomplete_snippets.append({
                    "index": i,
                    "issues": snippet_issues
                })
        
        if incomplete_snippets:
            issues.append(f"Incomplete code snippets: {len(incomplete_snippets)}")
            score -= min(30, len(incomplete_snippets) * 10)
        
        score = max(0, score)
        
        if len(snippets) == 0:
            level = EvaluationLevel.WARNING
            message = "No code snippets provided"
        elif score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Code snippets are incomplete"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Code snippets need improvement"
        else:
            level = EvaluationLevel.INFO
            message = "Code snippets are complete"
        
        suggestions = []
        if len(snippets) < min_snippets:
            suggestions.append(f"Add more code snippets (current: {len(snippets)}, minimum: {min_snippets})")
        for snippet_info in incomplete_snippets[:3]:
            suggestions.append(f"Fix snippet {snippet_info['index']}: {', '.join(snippet_info['issues'])}")
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "snippets_count": len(snippets),
                "incomplete_snippets": incomplete_snippets,
                "issues": issues
            },
            suggestions=suggestions
        )


# 语料集指标
class FieldMissingDistributionMetric(CollectionMetric):
    """字段缺失分布统计指标"""

    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("field_missing_distribution", weight, enabled)

    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        missing_fields_list = collected_info.get("missing_fields", [])
        corpus_ids = collected_info.get("corpus_id", [])

        # 统计字段缺失分布
        field_missing_count = {}
        for missing_fields in missing_fields_list:
            for field in missing_fields:
                field_missing_count[field] = field_missing_count.get(field, 0) + 1

        # 计算统计指标
        total_corpus = len(missing_fields_list)
        most_missing_fields = sorted(field_missing_count.items(),
                                   key=lambda x: x[1], reverse=True)[:5]

        # 计算分数（基于字段缺失的严重程度）
        if total_corpus == 0:
            score = 100
        else:
            # 分数基于平均字段完整性
            completion_rates = collected_info.get("field_completion_rate", [])
            avg_completion = sum(completion_rates) / len(completion_rates) if completion_rates else 1.0
            score = avg_completion * 100

        # 确定级别
        if score >= 80:
            level = EvaluationLevel.INFO
            message = "字段完整性良好"
        elif score >= 60:
            level = EvaluationLevel.WARNING
            message = "字段完整性中等，建议改进"
        else:
            level = EvaluationLevel.CRITICAL
            message = "字段完整性较差，需要重点改进"

        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "total_corpus": total_corpus,
                "most_missing_fields": most_missing_fields,
                "field_missing_distribution": field_missing_count,
                "average_completion_rate": avg_completion if total_corpus > 0 else 1.0
            }
        )

    def required_info_keys(self) -> List[str]:
        return ["missing_fields", "corpus_id", "field_completion_rate"]


class CompletenessPatternAnalysisMetric(CollectionMetric):
    """完整性模式分析指标"""

    def __init__(self, weight: float = 0.8, enabled: bool = True):
        super().__init__("completeness_pattern_analysis", weight, enabled)

    def calculate(self, collected_info: Dict[str, List[Any]]) -> MetricResult:
        completion_rates = collected_info.get("field_completion_rate", [])
        corpus_ids = collected_info.get("corpus_id", [])

        if not completion_rates:
            return self.create_result(
                score=0,
                level=EvaluationLevel.CRITICAL,
                message="无法分析完整性模式：缺少数据",
                dimension=EvaluationDimension.COMPLETENESS,
                details={"error": "No completion rate data available"}
            )

        # 分析完整性分布
        excellent_count = len([r for r in completion_rates if r >= 0.9])
        good_count = len([r for r in completion_rates if 0.7 <= r < 0.9])
        fair_count = len([r for r in completion_rates if 0.5 <= r < 0.7])
        poor_count = len([r for r in completion_rates if r < 0.5])

        total_count = len(completion_rates)
        avg_completion = sum(completion_rates) / total_count

        # 计算分数
        score = avg_completion * 100

        # 确定级别和消息
        if score >= 85:
            level = EvaluationLevel.INFO
            message = f"完整性模式优秀，平均完整率 {avg_completion:.1%}"
        elif score >= 70:
            level = EvaluationLevel.WARNING
            message = f"完整性模式良好，平均完整率 {avg_completion:.1%}"
        else:
            level = EvaluationLevel.CRITICAL
            message = f"完整性模式需要改进，平均完整率 {avg_completion:.1%}"

        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "total_corpus": total_count,
                "average_completion_rate": avg_completion,
                "completion_distribution": {
                    "excellent": excellent_count,
                    "good": good_count,
                    "fair": fair_count,
                    "poor": poor_count
                }
            }
        )

    def required_info_keys(self) -> List[str]:
        return ["field_completion_rate", "corpus_id"]


# 完整性维度
class CompletenessDimension(Dimension):
    """完整性维度，支持单语料和语料集指标"""

    def __init__(self):
        super().__init__(EvaluationDimension.COMPLETENESS)

        # 注册单语料指标
        self.register_corpus_metric(RequiredFieldsMetric())
        self.register_corpus_metric(ContentStructureMetric())
        self.register_corpus_metric(TestStepsCompletenessMetric())
        self.register_corpus_metric(CodeSnippetsCompletenessMetric())

        # 注册语料集指标
        self.register_collection_metric(FieldMissingDistributionMetric())
        self.register_collection_metric(CompletenessPatternAnalysisMetric())


